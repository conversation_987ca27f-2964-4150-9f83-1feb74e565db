import asyncio
import socketio
from aiohttp import web

sio = socketio.AsyncServer(cors_allowed_origins="*")
app = web.Application()
sio.attach(app)


@sio.on('connect')
async def connect(sid, environ):
    print('connect ', sid)


@sio.on('user_input')
async def message(sid, data):
    print(f"Received URL: {data}")
    for i in range(1, 6):
        await sio.emit('answer', f'我收到了您的消息，这是第{i}条回复')
        await asyncio.sleep(5)


@sio.on('disconnect')
async def disconnect(sid):
    print('disconnect ', sid)


if __name__ == '__main__':
    web.run_app(app, port=5100)
