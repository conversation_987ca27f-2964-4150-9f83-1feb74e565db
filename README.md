# py-qj-robots

[简体中文](https://github.com/QJ-ROBOTS/perception-python-sdk/wiki/%E5%8D%83%E8%AF%80%C2%B7%E6%84%9F%E7%9F%A5%E5%A4%A7%E6%A8%A1%E5%9E%8B) | [EN](https://github.com/QJ-ROBOTS/perception-python-sdk/wiki/QJ-PERCEPTION-MODEL)


QJ Robots Python SDK provides powerful machine vision perception capabilities, supporting object detection, image segmentation, attribute description, angle prediction, keypoint detection, and grasp point prediction for 2D/3D images.

## Requirements

- Python 3.x
- Dependencies: requests>=2.26.0, python-dotenv>=0.19.0

## Installation

```bash
pip install py-qj-robots
```

## Configuration

The following environment variables need to be configured before using the SDK:

- QJ_APP_ID: Application ID
- QJ_APP_SECRET: Application Secret

You can click [here](https://qj-robots.feishu.cn/share/base/form/shrcnzmXqHZsyw5AKi6oIuCKf4J) to obtain your Application ID and Secret.

You can configure these variables in two ways:

1. Create a .env file:
```
QJ_APP_ID=your_app_id
QJ_APP_SECRET=your_app_secret
```

2. Using export command:
```bash
export QJ_APP_ID=your_app_id
export QJ_APP_SECRET=your_app_secret
```

## Quick Start

### Synchronous Mode (Traditional)

```python
from dotenv import load_dotenv
from py_qj_robots import Perception
import os

# Load environment variables
load_dotenv()

# Initialize Perception instance (sync mode)
perception = Perception()

# Perform 2D image detection
result = perception.check_image(
    image_type="2D",
    image_url="http://example.com/image.jpg",
    object_names=["bottle", "cup"]
)
print(f"Detection result: {result}")
```

### Asynchronous Mode (High Performance)

For high-throughput scenarios, use async mode to achieve 10-15x performance improvement:

```python
from dotenv import load_dotenv
from py_qj_robots import Perception
import time

# Load environment variables
load_dotenv()

# Initialize Perception with async mode enabled
perception = Perception(enable_async_mode=True, max_workers=20)

# Define callback function
def result_callback(result, error):
    if error:
        print(f"Task failed: {error}")
    else:
        print(f"Task completed: {result.get('taskStatus', 'Unknown')}")

# Submit task asynchronously (returns immediately)
task_id = perception.check_image_async(
    image_type="2D",
    image_url="http://example.com/image.jpg",
    object_names=["bottle", "cup"],
    callback=result_callback
)

print(f"Task submitted: {task_id}")
# Continue with other work while task processes in background
time.sleep(5)  # Wait for completion
```

## Performance Comparison

| Mode | QPS | Use Case |
|------|-----|----------|
| Synchronous | ~10 | Simple applications, immediate results needed |
| Asynchronous | ~150+ | High-throughput applications, batch processing |

## API Documentation

### Perception Class

#### Constructor

```python
def __init__(self, enable_async_mode: bool = False, max_workers: int = 10)
```

Parameters:
- enable_async_mode: Enable asynchronous mode for high performance
- max_workers: Number of worker threads for async processing

#### check_image
Detect target objects in the image.

```python
def check_image(image_type: str, image_url: str, object_names: Union[str, List[str]], depth_url: Optional[str] = None) -> Dict
```

Parameters:
- image_type: Image type, '2D' or '3D'
- image_url: Image URL
- object_names: Names of objects to detect, can be a string or list of strings
- depth_url: Depth image URL (required only when image_type is '3D')

#### split_image
Segment target objects in the image.

```python
def split_image(image_type: str, image_url: str, object_names: Union[str, List[str]], depth_url: Optional[str] = None) -> Dict
```

Return value includes:
- boxes: Bounding box coordinates [x1,y1,x2,y2]
- masks: Mask image URLs and data
- croppedImagesListBbox: Cropped image URLs
- labels: Detected object labels
- scores: Confidence scores

#### props_describe
Get attribute descriptions of objects in the image.

```python
def props_describe(image_type: str, image_url: str, object_names: Union[str, List[str]], questions: Union[str, List[str]], depth_url: Optional[str] = None) -> Dict
```

#### angle_prediction
Predict object angles.

```python
def angle_prediction(image_type: str, image_url: str, object_names: Union[str, List[str]], depth_url: Optional[str] = None) -> Dict
```

#### key_point_prediction
Predict object keypoints.

```python
def key_point_prediction(image_type: str, image_url: str, object_names: Union[str, List[str]], depth_url: Optional[str] = None) -> Dict
```

#### grab_point_prediction
Predict object grasp points.

```python
def grab_point_prediction(image_type: str, image_url: str, object_names: Union[str, List[str]], depth_url: Optional[str] = None) -> Dict
```

#### full_perception
Perform complete perception analysis, including all features.

```python
def full_perception(image_type: str, image_url: str, object_names: Union[str, List[str]], questions: Union[str, List[str]], depth_url: Optional[str] = None) -> Dict
```

Return value includes all perception results:
- angles: Angle information
- boxes: Bounding boxes
- masks: Segmentation masks
- points: Keypoints
- grasps: Grasp points
- answers: Attribute descriptions
- etc.

### Asynchronous Methods

All perception methods have async versions with `_async` suffix:

- `check_image_async()`
- `split_image_async()`
- `props_describe_async()`
- `angle_prediction_async()`
- `key_point_prediction_async()`
- `grab_point_prediction_async()`
- `full_perception_async()`

#### Async Method Parameters

All async methods accept additional parameters:
- `callback`: Optional callback function called when task completes
- `timeout`: Timeout for task completion (default: 30 seconds)

#### Async Method Return

Async methods return a `task_id` string immediately, allowing you to:
1. Continue with other work
2. Track task progress
3. Get results via callback or manual polling

```python
# Manual polling example
task_id = perception.check_image_async(...)
result = perception.get_task_result_sync(task_id)
```

## Examples

- `async_usage_example.py`: Comprehensive async usage examples
- `high_performance_test.py`: Performance comparison between sync and async modes
- `verify_installation.py`: Installation verification script

## More Information

Visit [QJ Robots Official Website](https://www.qj-robots.com/) for more information.