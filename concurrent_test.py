import os
import statistics
import time
from concurrent.futures import ThreadPoolExecutor
import random
from dotenv import load_dotenv
import datetime
import csv
from py_qj_robots import Perception
from collections import defaultdict
import asyncio
import aiohttp
import logging

_original_factory = logging.getLogRecordFactory()

def log_with_time_offset(logger, level, msg, offset_sec):
    """只对一条日志记录进行时间偏移"""
    def custom_factory(*args, **kwargs):
        record = _original_factory(*args, **kwargs)
        record.created -= offset_sec
        record.msecs = (record.created - int(record.created)) * 1000
        record.relativeCreated = (record.created - logging._startTime) * 1000
        return record

    logging.setLogRecordFactory(custom_factory)
    logger.log(level, msg)
    logging.setLogRecordFactory(_original_factory)

# qj_app_id = 'app_8eywhz7enwziuxwws8ubgspxz4rm'
# qj_app_secret = 'Kq8AxCnKESBiEUiHYYGwCFaYy7gQJ4XC'
qj_app_id = 'app_9sqvq3wwvqb7zckj8azr93r2pjuu'
qj_app_secret = 'yFG5dZLwc3AmPRGY4ubeRF27jkuGzpvs'


def init_perception():
    os.environ['QJ_APP_ID'] = qj_app_id
    os.environ['QJ_APP_SECRET'] = qj_app_secret

    # 检查必要的环境变量
    required_vars = ['QJ_APP_ID', 'QJ_APP_SECRET']
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    if missing_vars:
        raise ValueError(f'缺少必要的环境变量: {", ".join(missing_vars)}')

    # 初始化Perception实例
    return Perception()

IMAGE_DATA_SETS = [
    {
        "image_url": "https://cos-cdn-v1.qj-robots.com/temp/3Fvk3RNcTGeJP5GWEmRqjh8Zb23NbtL2",
        "object_names": ["person", "bicycle"]
    },
    {
        "image_url": "https://cos-cdn-v1.qj-robots.com/temp/A4G2HAFFTSgjQLue729qGuU63SmQr9sx",
        "object_names": ["apple", "orange"]
    },
    {
        "image_url": "https://cos-cdn-v1.qj-robots.com/temp/gnFJUQmi3JUrcmyFxLqb6PxH3MZG8wL5",
        "object_names": ["dog", "scissors"]
    },
    {
        "image_url": "https://cos-cdn-v1.qj-robots.com/temp/Uw3ZbpSu4iUYNBzqeZbTxSriQhFktZqX",
        "object_names": ["table", "chair"]
    },
    {
        "image_url": "https://cos-cdn-v1.qj-robots.com/temp/EUkqsuHmCABEsXKbMCMtVPzznsrzsXX2",
        "object_names": ["car", "cat"]
    }
    # {
    #     "image_url": "https://cos-cdn-v1.qj-robots.com/temp/gRdH9JyM5rNVn2pNTbwFTbytLHnwyckS",
    #     "object_names": ["car", "cat"]
    # }
]



def test_2d_image_detection(perception, image_url, object_names):
    # 测试2/3D图像检测

    try:
        # 执行图像检测
        # _start_time = time.time()

        result = perception.check_image(
            image_type="2D",
            image_url=image_url,
            object_names=object_names
        )
        # time_cost = time.time() - _start_time
        # log_with_time_offset(logger, logging.INFO, f"TimeCostTest_Point1: {result['taskId']}", offset_sec=time_cost)
        # logger.info(f"TimeCostTest_Point31: {result['taskId']}")
        return True, result['taskId']

        # 测试3D图像检测
    #     result_3d = perception.check_image(
    #         image_type="3D",
    #         image_url=image_url,
    #         depth_url=image_url,
    #         object_names=["box", "ball"]
    #     )
    #     print(f"3D图像检测成功: {result_3d['taskStatus']}")
    #     return True
    except Exception as e:
        print(f"2D图像检测失败: {str(e)}")
        return False, ''

def test_split_image(perception, image_url, object_names):
    # 测试图像分割
    try:
        result = perception.split_image(
            image_type="2D",
            image_url=image_url,
            object_names=object_names
        )
        print(f"图像分割成功: {result['taskStatus']}")
        return True
    except Exception as e:
        print(f"图像分割失败: {str(e)}")
        return False

def test_props_describe(perception, image_url, object_names):
    # 测试物体属性描述
    try:
        result = perception.props_describe(
            image_type="2D",
            image_url=image_url,
            object_names=object_names,
            questions=[f"{object_names[0]}:什么颜色", f"{object_names[1]}:什么材质"]
        )
        print(f"属性描述成功: {result['taskStatus'], result['taskResult']['answers']}")
        return True
    except Exception as e:
        print(f"属性描述失败: {str(e)}")
        return False

def test_angle_prediction(perception, image_url, object_names):
    # 测试角度预测
    try:
        result = perception.angle_prediction(
            image_type="2D",
            image_url=image_url,
            object_names=object_names
        )
        print(f"角度预测成功: {result['taskStatus']}")
        return True
    except Exception as e:
        print(f"角度预测失败: {str(e)}")
        return False

def test_key_point_prediction(perception, image_url, object_names):
    # 测试关键点预测
    try:
        result = perception.key_point_prediction(
            image_type="2D",
            image_url=image_url,
            object_names=object_names
        )
        print(f"关键点预测成功: {result['taskStatus']}")
        return True
    except Exception as e:
        print(f"关键点预测失败: {str(e)}")
        return False

def test_grab_point_prediction(perception, image_url, object_names):
    # 测试抓取点预测
    try:
        result = perception.grab_point_prediction(
            image_type="2D",
            image_url=image_url,
            object_names=object_names
        )
        print(f"抓取点预测成功: {result['taskStatus']}")
        return True
    except Exception as e:
        print(f"抓取点预测失败: {str(e)}")
        return False

def test_full_perception(perception, image_url, object_names):
    # 测试全功能感知
    try:
        result = perception.full_perception(
            image_type="2D",
            image_url=image_url,
            object_names=object_names,
            questions=[f"{object_names[0]}:什么颜色", f"{object_names[1]}:什么材质"]
        )
        print(f"全功能感知成功: {result['taskStatus']}")
        return True
    except Exception as e:
        print(f"全功能感知失败: {str(e)}")
        return False


async def run_stress_test(concurrency, total_requests, api_config, report_filename="stress_test_report.csv", throughput_filename="throughput_report.csv"):
    """
    压力测试函数
    :param concurrency: 并发度
    :param total_requests: 总请求数
    :param api_config: API配置列表，格式: [(api_function, weight), ...]
    :return: 测试报告
    """

    # 验证权重配置
    total_weight = sum(weight for _, weight in api_config)
    if total_weight <= 0:
        raise ValueError("API权重总和必须大于0")

    # 准备权重选择器
    cumulative_weights = []
    current_weight = 0
    for func, weight in api_config:
        current_weight += weight
        cumulative_weights.append(current_weight)

    # 创建API名称映射
    api_names = {func: func.__name__ for func, _ in api_config}

    results = []
    api_stats = {name: {"calls": 0, "success": 0, "latencies": []}
                 for name in api_names.values()}
    throughput_data = defaultdict(lambda: defaultdict(int))

    # 异步任务信号量控制并发度
    sem = asyncio.Semaphore(concurrency)

    async def make_request(delay=0):
        await asyncio.sleep(delay)
        async with sem:
            selected_data = random.choice(IMAGE_DATA_SETS)
            image_url = selected_data["image_url"]
            object_names = selected_data["object_names"]

            # 按权重随机选择API
            rand_val = random.uniform(0, total_weight)
            selected_index = 0
            for i, weight in enumerate(cumulative_weights):
                if rand_val <= weight:
                    selected_index = i
                    break

            api_func, _ = api_config[selected_index]
            api_name = api_names[api_func]

            perception = init_perception()
            start_time = time.time()
            timestamp = datetime.datetime.fromtimestamp(start_time)
            minute_timestamp = timestamp.strftime("%Y-%m-%d %H:%M")

            try:
                loop = asyncio.get_running_loop()
                _start_time = time.time()
                _time = datetime.datetime.fromtimestamp(time.time()).strftime("%Y-%m-%d %H:%M:%S")
                success, task_id = await loop.run_in_executor(None, lambda: api_func(perception, image_url, object_names))
                latency = time.time() - start_time
                print('耗时: ', latency)
                _time = datetime.datetime.fromtimestamp(time.time()).strftime("%Y-%m-%d %H:%M:%S")

                time_cost = time.time() - _start_time
                log_with_time_offset(logger, logging.INFO, f"TimeCostTest_Point1: {task_id}", offset_sec=time_cost)
                logger.info(f"TimeCostTest_Point31: {task_id}")


                # 记录结果
                results.append((api_name, success, latency))
                throughput_data[minute_timestamp][api_name] += 1
                if success:
                    throughput_data[minute_timestamp][f"{api_name}_success"] += 1

                # 更新API统计
                stats = api_stats[api_name]
                stats["calls"] += 1
                if success:
                    stats["success"] += 1
                stats["latencies"].append(latency)

                return True
            except Exception as e:
                latency = time.time() - start_time
                results.append((api_name, False, latency))
                throughput_data[minute_timestamp][api_name] += 1
                print(f"请求失败: {str(e)}")
                return False

    # 创建所有任务
    tasks = [make_request() for _ in range(total_requests)]
    start_time = time.time()

    # 并行执行所有任务
    await asyncio.gather(*tasks)

    end_time = time.time()
    total_time = end_time - start_time

    # 计算全局统计
    success_count = sum(1 for _, success, _ in results if success)
    failure_count = len(results) - success_count
    latencies = [latency for _, _, latency in results]

    # 计算指标
    min_latency = min(latencies) if latencies else 0
    max_latency = max(latencies) if latencies else 0
    avg_latency = statistics.mean(latencies) if latencies else 0

    # 计算API耗时分布
    latency_distribution = {'lt_3': 0, '3_to_5': 0, '5_to_10': 0, 'gt_10': 0}
    for latency in latencies:
        if latency < 3:
            latency_distribution['lt_3'] += 1
        elif 3 <= latency < 5:
            latency_distribution['3_to_5'] += 1
        elif 5 <= latency < 10:
            latency_distribution['5_to_10'] += 1
        else:
            latency_distribution['gt_10'] += 1

    # 计算API成功率
    for stats in api_stats.values():
        calls = stats["calls"]
        if calls > 0:
            stats["success_rate"] = stats["success"] / calls * 100
            stats["min_latency"] = min(stats["latencies"])
            stats["max_latency"] = max(stats["latencies"])
            stats["avg_latency"] = statistics.mean(stats["latencies"])
        else:
            stats.update({"success_rate": 0, "min_latency": 0, "max_latency": 0, "avg_latency": 0})

    report = {
        'timestamp': datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        'concurrency': concurrency,
        'total_requests': total_requests,
        'success_count': success_count,
        'failure_count': failure_count,
        'min_latency': min_latency,
        'max_latency': max_latency,
        'avg_latency': avg_latency,
        'latency_distribution': latency_distribution,
        'total_time': total_time,
        'requests_per_second': total_requests / total_time if total_time > 0 else 0,
        'api_stats': api_stats,
        'throughput_data': dict(throughput_data)
    }
    # 保存报告到CSV
    save_report_to_csv(report, report_filename)
    save_throughput_to_csv(report, throughput_filename)

    return report


def save_report_to_csv(report, filename="stress_test_report.csv"):
    file_exists = os.path.isfile(filename)
    # 创建CSV行数据
    csv_row = {
        'timestamp': report['timestamp'],
        'concurrency': report['concurrency'],
        'total_requests': report['total_requests'],
        'success_count': report['success_count'],
        'failure_count': report['failure_count'],
        'success_rate': report['success_count'] / report['total_requests'] * 100 if report['total_requests'] > 0 else 0,
        'min_latency': report['min_latency'],
        'max_latency': report['max_latency'],
        'avg_latency': report['avg_latency'],
        'total_time': report['total_time'],
        'requests_per_second': report['requests_per_second'],
        'latency_lt_3': report['latency_distribution']['lt_3'],
        'latency_3_to_5': report['latency_distribution']['3_to_5'],
        'latency_5_to_10': report['latency_distribution']['5_to_10'],
        'latency_gt_10': report['latency_distribution']['gt_10'],
    }

    # 添加每个API的统计
    for api_name, stats in report['api_stats'].items():
        prefix = f"{api_name}_"
        csv_row.update({
            f"{prefix}calls": stats['calls'],
            f"{prefix}success": stats['success'],
            f"{prefix}success_rate": stats['success_rate'],
            f"{prefix}min_latency": stats['min_latency'],
            f"{prefix}max_latency": stats['max_latency'],
            f"{prefix}avg_latency": stats['avg_latency']
        })

    # 写入CSV文件
    with open(filename, 'a', newline='', encoding='utf-8') as f:
        writer = csv.DictWriter(f, fieldnames=csv_row.keys())

        # 如果文件不存在，写入表头
        if not file_exists:
            writer.writeheader()

        writer.writerow(csv_row)

    print(f"测试报告已保存到 {filename}")


def save_throughput_to_csv(report, filename="throughput_report.csv"):
    file_exists = os.path.isfile(filename)
    # 提取吞吐量数据
    throughput_data = report['throughput_data']
    concurrency = report['concurrency']

    # 获取所有API名称
    api_names = list(report['api_stats'].keys())

    # 创建CSV行数据
    csv_rows = []

    for minute in sorted(throughput_data.keys()):
        row = {
            'concurrency': concurrency,
            'minute': minute
        }

        # 添加每个API的调用次数和成功次数
        for api_name in api_names:
            # 总调用次数
            total_calls = throughput_data[minute].get(api_name, 0)
            row[f"{api_name}_calls"] = total_calls

            # 成功次数
            success_calls = throughput_data[minute].get(f"{api_name}_success", 0)
            row[f"{api_name}_success"] = success_calls

            # 计算吞吐量 (每分钟调用次数)
            row[f"{api_name}_throughput"] = total_calls
            if success_calls > 0:
                row[f"{api_name}_success_rate"] = (success_calls / total_calls) * 100
            else:
                row[f"{api_name}_success_rate"] = 0

        # 计算总吞吐量
        total_calls = sum(throughput_data[minute].get(api_name, 0) for api_name in api_names)
        row['total_throughput'] = total_calls
        row['requests_per_second'] = report['requests_per_second']
        csv_rows.append(row)

    # 写入CSV文件
    with open(filename, 'a', newline='', encoding='utf-8') as f:
        if csv_rows:
            fieldnames = list(csv_rows[0].keys())
            writer = csv.DictWriter(f, fieldnames=fieldnames)

            if not file_exists:
                writer.writeheader()

            writer.writerows(csv_rows)

    print(f"吞吐量报告已保存到 {filename}")

def configure_logging():
    folder_log = 'api_logs_debug'
    os.makedirs(folder_log, exist_ok=True)
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(processName)s - %(levelname)s - %(message)s",
        handlers=[
            logging.StreamHandler(),  # 输出到控制台
            logging.FileHandler(os.path.join(folder_log, 'concurrent_test_log.txt'))  # 保存到文件
        ]
    )

if __name__ == '__main__':
    configure_logging()
    logger = logging.getLogger()
    # 配置API和权重
    API_CONFIG = [
        (test_2d_image_detection, 10),
        (test_split_image, 0),
        (test_props_describe, 0),
        (test_angle_prediction, 0),
        (test_key_point_prediction, 0),
        (test_grab_point_prediction, 0),
        (test_full_perception, 0)
    ]

    # 执行压力测试
    report = asyncio.run(
        run_stress_test(
            concurrency=1,
            total_requests=100,
            api_config=API_CONFIG,
            report_filename="stress_report_2d.csv",
            throughput_filename="throughput_report_2d.csv"
        )
    )

    print(report)
    # concurrency_levels = [5, 10, 15, 20, 25, 30, 35, 40, 45, 50]
    # # 遍历测试
    # for concurrency in concurrency_levels:
    #     print(f"\n\n=============== 开始并发度 {concurrency} 测试 ===============")
    #     # 执行压力测试
    #     report = asyncio.run(
    #         run_stress_test(
    #             concurrency=concurrency,
    #             total_requests=800,
    #             api_config=API_CONFIG,
    #             report_filename="/home/<USER>/debug/xx/qj_robots/test_report/stress_report_2d.csv",
    #             throughput_filename="/home/<USER>/debug/xx/qj_robots/test_report/throughput_report_2d.csv"
    #         )
    #     )

    # 打印测试报告
    print("\n=============== 压力测试报告 ===============")
    print(f"测试时间: {report['timestamp']}")
    print(f"并发度: {report['concurrency']}")
    print(f"总请求数: {report['total_requests']}")
    print(f"成功次数: {report['success_count']}")
    print(f"失败次数: {report['failure_count']}")
    print(f"总成功率: {report['success_count'] / report['total_requests'] * 100:.2f}%")
    print(f"总耗时: {report['total_time']:.2f}秒")
    print(f"吞吐率: {report['requests_per_second']:.2f} 请求/秒")
    print(f"最小耗时: {report['min_latency']:.4f}秒")
    print(f"最大耗时: {report['max_latency']:.4f}秒")
    print(f"平均耗时: {report['avg_latency']:.4f}秒")

    # 打印API详细统计
    print("\n=============== API详细统计 ===============")
    for api_name, stats in report['api_stats'].items():
        print(f"\nAPI: {api_name}")
        print(f"  调用次数: {stats['calls']} ({stats['calls'] / report['total_requests'] * 100:.1f}%)")
        print(f"  成功次数: {stats['success']}")
        print(f"  成功率: {stats['success_rate']:.2f}%")
        print(f"  平均耗时: {stats['avg_latency']:.4f}秒")
        print(f"  最小耗时: {stats['min_latency']:.4f}秒")
        print(f"  最大耗时: {stats['max_latency']:.4f}秒")

    # 打印耗时分布
    print("\n=============== 耗时分布 ===============")
    dist = report['latency_distribution']
    total = report['total_requests']
    print(f"<3秒: {dist['lt_3']}次 ({dist['lt_3'] / total * 100:.2f}%)")
    print(f"3-5秒: {dist['3_to_5']}次 ({dist['3_to_5'] / total * 100:.2f}%)")
    print(f"5-10秒: {dist['5_to_10']}次 ({dist['5_to_10'] / total * 100:.2f}%)")
    print(f">10秒: {dist['gt_10']}次 ({dist['gt_10'] / total * 100:.2f}%)")
