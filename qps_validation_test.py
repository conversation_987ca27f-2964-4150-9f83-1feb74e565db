#!/usr/bin/env python3
"""
QPS检测逻辑验证测试脚本

用于验证修复后的QPS计算是否准确
"""

import os
import time
import threading
from dotenv import load_dotenv
from py_qj_robots import Perception


def init_perception_async(max_workers=10):
    """Initialize Perception with async mode enabled"""
    load_dotenv()
    
    required_vars = ['QJ_APP_ID', 'QJ_APP_SECRET']
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    if missing_vars:
        raise ValueError(f'缺少必要的环境变量: {", ".join(missing_vars)}')
    
    return Perception(enable_async_mode=True, max_workers=max_workers)


class SimpleQPSTracker:
    """简化的QPS跟踪器，用于验证逻辑"""
    
    def __init__(self):
        self.submitted_tasks = {}  # task_id -> submit_time
        self.completed_tasks = {}  # task_id -> (complete_time, success)
        self.lock = threading.Lock()
        self.test_start_time = None
    
    def start_test(self):
        """开始测试"""
        with self.lock:
            self.test_start_time = time.time()
            print(f"🚀 测试开始时间: {self.test_start_time}")
    
    def record_submit(self, task_id):
        """记录任务提交"""
        submit_time = time.time()
        with self.lock:
            self.submitted_tasks[task_id] = submit_time
            print(f"📤 提交任务 {task_id} 在 {submit_time - self.test_start_time:.3f}s")
    
    def record_completion(self, task_id, success=True):
        """记录任务完成"""
        complete_time = time.time()
        with self.lock:
            self.completed_tasks[task_id] = (complete_time, success)
            submit_time = self.submitted_tasks.get(task_id, self.test_start_time)
            response_time = complete_time - submit_time
            print(f"✅ 完成任务 {task_id} 在 {complete_time - self.test_start_time:.3f}s (响应时间: {response_time:.3f}s)")
    
    def get_stats(self):
        """获取统计信息"""
        with self.lock:
            if not self.submitted_tasks or self.test_start_time is None:
                return {}
            
            current_time = time.time()
            total_submitted = len(self.submitted_tasks)
            total_completed = len(self.completed_tasks)
            successful_tasks = sum(1 for _, (_, success) in self.completed_tasks.items() if success)
            
            # 计算提交时间范围
            submit_times = list(self.submitted_tasks.values())
            submit_start = min(submit_times)
            submit_end = max(submit_times)
            submit_duration = submit_end - submit_start if submit_start != submit_end else 0.001  # 避免除零
            
            # 计算完成时间范围
            if self.completed_tasks:
                complete_times = [complete_time for complete_time, _ in self.completed_tasks.values()]
                completion_end = max(complete_times)
                total_duration = completion_end - self.test_start_time
            else:
                total_duration = current_time - self.test_start_time
            
            # QPS计算
            submit_qps = total_submitted / submit_duration if submit_duration > 0 else 0
            overall_qps = successful_tasks / total_duration if total_duration > 0 else 0
            
            # 响应时间统计
            response_times = []
            for task_id, (complete_time, success) in self.completed_tasks.items():
                if task_id in self.submitted_tasks:
                    response_time = complete_time - self.submitted_tasks[task_id]
                    response_times.append(response_time)
            
            avg_response_time = sum(response_times) / len(response_times) if response_times else 0
            
            return {
                'total_submitted': total_submitted,
                'total_completed': total_completed,
                'successful_tasks': successful_tasks,
                'submit_duration': submit_duration,
                'total_duration': total_duration,
                'submit_qps': submit_qps,
                'overall_qps': overall_qps,
                'avg_response_time': avg_response_time,
                'completion_rate': total_completed / total_submitted if total_submitted > 0 else 0
            }


def test_qps_accuracy(total_requests=10, max_workers=5):
    """测试QPS计算准确性"""
    print(f"=== QPS准确性测试: {total_requests} 请求, {max_workers} 工作线程 ===")
    
    tracker = SimpleQPSTracker()
    perception = init_perception_async(max_workers=max_workers)
    
    image_url = "https://cos-cdn-v1.qj-robots.com/temp/3Fvk3RNcTGeJP5GWEmRqjh8Zb23NbtL2"
    object_names = ["bottle", "cup"]
    
    def create_callback(request_index):
        def callback(result, error):
            success = error is None
            # 从result中获取真实的task_id
            real_task_id = None
            if result and 'taskId' in result:
                real_task_id = result['taskId']

            if real_task_id:
                tracker.record_completion(real_task_id, success)
            else:
                print(f"⚠️ 无法获取任务ID，请求索引: {request_index}")

            if error:
                print(f"❌ 任务失败 (请求 {request_index}): {error}")
        return callback
    
    # 开始测试
    tracker.start_test()
    
    print("\n--- 阶段1: 提交任务 ---")
    submitted_task_ids = []
    
    for i in range(total_requests):
        try:
            task_id = perception.check_image_async(
                image_type="2D",
                image_url=image_url,
                object_names=object_names,
                callback=create_callback(i),  # 传递请求索引而不是task_id字符串
                timeout=60
            )
            tracker.record_submit(task_id)
            submitted_task_ids.append(task_id)
            
            # 稍微间隔一下，让提交时间有差异
            time.sleep(0.1)
            
        except Exception as e:
            print(f"❌ 提交任务 {i} 失败: {e}")
    
    print(f"\n--- 阶段2: 等待完成 (最多60秒) ---")
    
    # 等待任务完成
    max_wait = 60
    start_wait = time.time()
    
    while time.time() - start_wait < max_wait:
        stats = tracker.get_stats()
        print(f"进度: {stats.get('total_completed', 0)}/{stats.get('total_submitted', 0)} 完成")
        
        if stats.get('total_completed', 0) >= total_requests:
            break
        
        time.sleep(2)
    
    # 最终统计
    final_stats = tracker.get_stats()
    
    print(f"\n=== 最终统计结果 ===")
    print(f"提交任务数: {final_stats['total_submitted']}")
    print(f"完成任务数: {final_stats['total_completed']}")
    print(f"成功任务数: {final_stats['successful_tasks']}")
    print(f"完成率: {final_stats['completion_rate']:.2%}")
    print(f"提交时间跨度: {final_stats['submit_duration']:.3f} 秒")
    print(f"总测试时间: {final_stats['total_duration']:.3f} 秒")
    print(f"提交QPS: {final_stats['submit_qps']:.2f} (任务/秒)")
    print(f"整体QPS: {final_stats['overall_qps']:.2f} (成功任务/秒)")
    print(f"平均响应时间: {final_stats['avg_response_time']:.3f} 秒")
    
    # 验证QPS计算的合理性
    print(f"\n=== QPS验证 ===")
    expected_submit_qps = total_requests / final_stats['submit_duration']
    expected_overall_qps = final_stats['successful_tasks'] / final_stats['total_duration']
    
    print(f"预期提交QPS: {expected_submit_qps:.2f}")
    print(f"计算提交QPS: {final_stats['submit_qps']:.2f}")
    print(f"提交QPS误差: {abs(expected_submit_qps - final_stats['submit_qps']):.4f}")
    
    print(f"预期整体QPS: {expected_overall_qps:.2f}")
    print(f"计算整体QPS: {final_stats['overall_qps']:.2f}")
    print(f"整体QPS误差: {abs(expected_overall_qps - final_stats['overall_qps']):.4f}")
    
    return final_stats


def main():
    """主测试函数"""
    try:
        # 小规模测试验证逻辑
        test_qps_accuracy(total_requests=5, max_workers=3)
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
