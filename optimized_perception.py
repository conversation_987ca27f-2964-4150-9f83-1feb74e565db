#!/usr/bin/env python3
"""
优化的Perception类，专门用于并发性能测试

主要优化：
1. 使用连接池减少连接开销
2. 配置合适的超时参数
3. 优化轮询策略
4. 添加详细的性能监控
"""

import os
import time
import threading
import queue
from concurrent.futures import ThreadPoolExecutor
from typing import Optional, Callable, Dict, Union, List
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

from dotenv import load_dotenv
from py_qj_robots.authorization import Authorization


class OptimizedPerception:
    """优化的Perception类，专门用于高并发测试"""
    
    def __init__(self, enable_async_mode: bool = False, max_workers: int = 10,
                 connection_pool_size: int = 50, max_retries: int = 3):
        # Initialize authorization
        self.auth = Authorization()
        self.base_url = f"{self.auth.host}/perception"
        
        # 优化的HTTP会话配置
        self.session = requests.Session()
        
        # 配置重试策略
        retry_strategy = Retry(
            total=max_retries,
            backoff_factor=0.3,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        
        # 配置HTTP适配器和连接池
        adapter = HTTPAdapter(
            pool_connections=connection_pool_size,
            pool_maxsize=connection_pool_size,
            max_retries=retry_strategy
        )
        
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
        # 设置默认超时
        self.session.timeout = (10, 30)  # (连接超时, 读取超时)
        
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.auth.get_access_token()}"
        }

        # Async mode configuration
        self.enable_async_mode = enable_async_mode
        self.max_workers = max_workers
        self._executor = None
        self._polling_tasks = {}  # task_id -> future
        self._result_callbacks = {}  # task_id -> callback
        
        # 性能监控
        self.performance_stats = {
            'submit_requests': 0,
            'submit_failures': 0,
            'polling_requests': 0,
            'polling_failures': 0,
            'total_submit_time': 0.0,
            'total_polling_time': 0.0
        }
        self.stats_lock = threading.Lock()

        if enable_async_mode:
            self._executor = ThreadPoolExecutor(max_workers=max_workers)
            self._polling_queue = queue.Queue()
            self._start_polling_worker()

    def _start_polling_worker(self):
        """Start background polling worker thread"""
        def polling_dispatcher():
            """Dispatcher that assigns polling tasks to thread pool"""
            while True:
                try:
                    task_info = self._polling_queue.get(timeout=1)
                    if task_info is None:  # Shutdown signal
                        break

                    # Submit polling task to thread pool for parallel processing
                    task_id, callback, timeout = task_info
                    self._executor.submit(self._poll_task_async, task_id, callback, timeout)

                    self._polling_queue.task_done()

                except queue.Empty:
                    continue
                except Exception as e:
                    print(f"Polling dispatcher error: {e}")

        self._polling_thread = threading.Thread(target=polling_dispatcher, daemon=True)
        self._polling_thread.start()

    def _poll_task_async(self, task_id: str, callback: Optional[Callable], timeout: int):
        """Poll a single task asynchronously"""
        try:
            result = self._poll_task_result(task_id, timeout)
            if callback:
                callback(result, None)
        except Exception as e:
            if callback:
                callback(None, e)

    def _get_task_result(self, task_id: str) -> Dict:
        """Get the result of a perception task"""
        polling_start = time.time()
        
        try:
            result = self._make_get_request(
                endpoint="/open-api/open-apis/app/perception/result",
                params={"task_id": task_id}
            )
            
            with self.stats_lock:
                self.performance_stats['polling_requests'] += 1
                self.performance_stats['total_polling_time'] += time.time() - polling_start
            
            return result
            
        except Exception as e:
            with self.stats_lock:
                self.performance_stats['polling_failures'] += 1
            raise e

    def _poll_task_result(self, task_id: str, timeout: int = 30) -> Dict:
        """Poll for task result until completion or timeout"""
        start_time = time.time()
        poll_interval = 0.1  # Start with 100ms interval
        max_interval = 2.0   # Max 2 second interval (优化：增加最大间隔)

        while True:
            # Check if polling has exceeded timeout
            if time.time() - start_time > timeout:
                raise TimeoutError(f"{task_id} => Polling exceeded {timeout} seconds timeout")

            # Get task result
            result = self._get_task_result(task_id)

            if result['taskStatus'] == 'SUBMIT_FAILED':
                raise RuntimeError(f"{task_id} => task submit failed,please retry later.")
            # Return if task is complete
            if result['taskStatus'] == 'DONE':
                return result

            # Adaptive polling interval - start fast, then slow down
            time.sleep(poll_interval)
            poll_interval = min(poll_interval * 1.3, max_interval)  # 优化：调整退避策略

    def _make_post_request(self, endpoint: str, data: Dict) -> Dict:
        """Make POST request to perception API"""
        submit_start = time.time()
        
        try:
            url = f"{self.auth.host}{endpoint}"
            response = self.session.post(url, json=data, headers=self.headers)
            response.raise_for_status()

            result = response.json()
            if result["code"] != 0:
                raise Exception(f"API request failed: {result['message']}")

            with self.stats_lock:
                self.performance_stats['submit_requests'] += 1
                self.performance_stats['total_submit_time'] += time.time() - submit_start

            return result["data"]
            
        except Exception as e:
            with self.stats_lock:
                self.performance_stats['submit_failures'] += 1
            raise e

    def _make_get_request(self, endpoint: str, params: Dict) -> Dict:
        """Make GET request to perception API"""
        url = f"{self.auth.host}{endpoint}"
        response = self.session.get(url, params=params, headers=self.headers)

        result = response.json()
        if result["code"] != 0:
            raise Exception(f"API request failed: {result['message']}")

        return result["data"]

    def submit_task_async(self, endpoint: str, data: Dict,
                         callback: Optional[Callable[[Dict, Exception], None]] = None,
                         timeout: int = 30) -> str:
        """Submit a perception task asynchronously"""
        if not self.enable_async_mode:
            raise RuntimeError("Async mode is not enabled. Initialize with enable_async_mode=True")

        # Submit task and get task ID
        result = self._make_post_request(endpoint, data)
        task_id = result['taskId']

        # Add to polling queue
        self._polling_queue.put((task_id, callback, timeout))

        return task_id

    def _validate_image_params(self, image_type: str, depth_url: Optional[str] = None):
        """Validate image parameters"""
        if image_type not in ["2D", "3D"]:
            raise ValueError("image_type must be '2D' or '3D'")
        
        if image_type == "3D" and not depth_url:
            raise ValueError("depth_url is required for 3D image type")

    def _prepare_request_data(self, image_type: str, image_url: str, 
                            object_names: Union[str, List[str]], 
                            depth_url: Optional[str] = None) -> Dict:
        """Prepare request data"""
        data = {
            "imageType": image_type,
            "imageUrl": image_url,
        }
        
        if isinstance(object_names, list):
            data["objectNames"] = ','.join(object_names)
        else:
            data["objectNames"] = object_names
        
        if depth_url:
            data["depthUrl"] = depth_url
        
        return data

    def check_image_async(self, image_type: str, image_url: str, object_names: Union[str, List[str]],
                         depth_url: Optional[str] = None,
                         callback: Optional[Callable[[Dict, Exception], None]] = None,
                         timeout: int = 30) -> str:
        """Async version of check_image"""
        self._validate_image_params(image_type, depth_url)
        data = self._prepare_request_data(image_type, image_url, object_names, depth_url)
        return self.submit_task_async("/open-api/open-apis/app/perception/check", data, callback, timeout)

    def get_performance_stats(self) -> Dict:
        """获取性能统计信息"""
        with self.stats_lock:
            stats = self.performance_stats.copy()
        
        # 计算平均时间
        if stats['submit_requests'] > 0:
            stats['avg_submit_time'] = stats['total_submit_time'] / stats['submit_requests']
        else:
            stats['avg_submit_time'] = 0
            
        if stats['polling_requests'] > 0:
            stats['avg_polling_time'] = stats['total_polling_time'] / stats['polling_requests']
        else:
            stats['avg_polling_time'] = 0
        
        # 计算成功率
        total_submits = stats['submit_requests'] + stats['submit_failures']
        stats['submit_success_rate'] = stats['submit_requests'] / total_submits if total_submits > 0 else 0
        
        total_polls = stats['polling_requests'] + stats['polling_failures']
        stats['polling_success_rate'] = stats['polling_requests'] / total_polls if total_polls > 0 else 0
        
        return stats

    def reset_performance_stats(self):
        """重置性能统计"""
        with self.stats_lock:
            self.performance_stats = {
                'submit_requests': 0,
                'submit_failures': 0,
                'polling_requests': 0,
                'polling_failures': 0,
                'total_submit_time': 0.0,
                'total_polling_time': 0.0
            }

    def __del__(self):
        """Cleanup resources"""
        if hasattr(self, '_executor') and self._executor:
            self._executor.shutdown(wait=False)
        if hasattr(self, '_polling_queue'):
            self._polling_queue.put(None)  # Shutdown signal
        if hasattr(self, 'session'):
            self.session.close()


def create_optimized_perception(max_workers: int = 20, connection_pool_size: int = 50) -> OptimizedPerception:
    """创建优化的Perception实例"""
    load_dotenv()
    
    required_vars = ['QJ_APP_ID', 'QJ_APP_SECRET']
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    if missing_vars:
        raise ValueError(f'缺少必要的环境变量: {", ".join(missing_vars)}')
    
    return OptimizedPerception(
        enable_async_mode=True, 
        max_workers=max_workers,
        connection_pool_size=connection_pool_size
    )
