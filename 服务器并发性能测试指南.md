# 感知SDK服务器并发性能测试指南

## 📋 测试目标

准确测试感知API服务器的并发处理能力，识别性能瓶颈和最佳配置参数。

## 🔍 SDK架构分析

### 关键架构特点

1. **异步模式架构**：
   - 使用 `ThreadPoolExecutor` 管理工作线程
   - 单独的轮询调度线程 + 线程池执行轮询任务
   - 任务提交和结果获取完全分离

2. **HTTP请求机制**：
   - 使用标准的 `requests` 库，**没有连接池配置**
   - 每个请求都是独立的HTTP连接
   - **没有设置超时参数**，可能导致连接堆积

3. **轮询机制**：
   - 自适应轮询间隔：100ms → 1s（指数退避）
   - 每个任务独立轮询线程
   - 轮询频率会影响服务器负载

### 🚨 影响并发测试准确性的关键因素

1. **连接开销**：每次请求创建新连接，增加延迟
2. **轮询负载**：大量轮询请求可能成为性能瓶颈
3. **客户端限制**：SDK的线程池大小限制了真实并发度
4. **网络延迟**：无法区分网络延迟和服务器处理时间

## 🎯 准确测试服务器并发性能的策略

### 1. 分离测试维度

#### A. 任务提交性能测试
**目标**：测试服务器接收和处理任务提交的能力

```python
# 突发提交测试
def test_submit_burst():
    # 使用线程池同时提交大量请求
    # 测量：提交QPS、提交成功率、提交延迟
    pass

# 持续提交测试  
def test_submit_sustained():
    # 长时间持续提交请求
    # 测量：稳定状态下的提交QPS
    pass
```

#### B. 任务处理性能测试
**目标**：测试服务器实际处理任务的能力

```python
# 处理能力测试
def test_processing_capacity():
    # 提交后停止新提交，专注测量处理速度
    # 测量：处理QPS、处理时间分布
    pass
```

#### C. 端到端性能测试
**目标**：测试完整的用户体验

```python
# 端到端测试
def test_end_to_end():
    # 模拟真实用户场景
    # 测量：总响应时间、用户感知QPS
    pass
```

### 2. 优化测试环境

#### A. 客户端优化
- **连接池**：使用HTTP连接池减少连接开销
- **超时配置**：设置合理的连接和读取超时
- **重试策略**：配置指数退避重试
- **轮询优化**：调整轮询间隔和策略

#### B. 测试参数配置
```python
# 推荐配置
OPTIMIZED_CONFIG = {
    'max_workers': 50,           # SDK线程池大小
    'connection_pool_size': 100, # HTTP连接池大小
    'submit_timeout': (5, 30),   # (连接超时, 读取超时)
    'polling_timeout': (3, 10),  # 轮询请求超时
    'max_polling_interval': 2.0, # 最大轮询间隔
    'retry_attempts': 3          # 重试次数
}
```

### 3. 测试场景设计

#### 场景1：突发并发测试
```python
# 测试服务器处理突发请求的能力
concurrency_levels = [5, 10, 20, 50, 100, 200]
requests_per_level = 50

for concurrency in concurrency_levels:
    # 同时提交 concurrency 个请求
    # 测量：提交QPS、处理QPS、响应时间分布
```

#### 场景2：持续负载测试
```python
# 测试服务器长时间处理负载的能力
sustained_concurrency = 20
test_duration = 300  # 5分钟

# 持续提交请求，维持指定并发度
# 测量：稳定状态性能、性能衰减情况
```

#### 场景3：压力测试
```python
# 找到服务器的性能极限
# 逐步增加并发度直到出现明显性能下降
# 识别：最大QPS、性能拐点、错误率阈值
```

## 📊 关键性能指标

### 1. 提交阶段指标
- **提交QPS**：每秒成功提交的任务数
- **提交延迟**：从发起请求到收到task_id的时间
- **提交成功率**：成功提交的请求占总请求的比例

### 2. 处理阶段指标
- **服务器QPS**：服务器每秒实际处理完成的任务数
- **处理时间**：服务器实际处理单个任务的时间
- **队列等待时间**：任务在服务器队列中的等待时间

### 3. 端到端指标
- **总响应时间**：从提交到获得结果的总时间
- **响应时间分布**：P50、P95、P99响应时间
- **用户感知QPS**：用户角度的每秒完成任务数

### 4. 系统稳定性指标
- **错误率**：各类错误的发生率
- **超时率**：超时请求的比例
- **资源利用率**：CPU、内存、网络使用情况

## 🔧 测试实施建议

### 1. 测试环境准备
```bash
# 安装依赖
pip install requests[security] urllib3

# 配置环境变量
export QJ_APP_ID="your_app_id"
export QJ_APP_SECRET="your_app_secret"

# 网络环境
# 确保网络稳定，最好在同一数据中心测试
```

### 2. 测试执行步骤

#### 步骤1：基线测试
```python
# 单线程测试，建立性能基线
python server_concurrency_test.py --concurrency 1 --requests 10
```

#### 步骤2：逐步增加并发
```python
# 逐步测试不同并发级别
for concurrency in [5, 10, 20, 30, 50]:
    python server_concurrency_test.py --concurrency $concurrency --requests 50
```

#### 步骤3：长时间稳定性测试
```python
# 测试长时间稳定性
python server_concurrency_test.py --sustained --concurrency 20 --duration 300
```

### 3. 结果分析

#### A. 性能曲线分析
- 绘制并发度 vs QPS曲线
- 识别性能拐点和最优并发度
- 分析响应时间随并发度的变化

#### B. 瓶颈识别
```python
# 分析各阶段耗时占比
submit_time_ratio = submit_time / total_time
processing_time_ratio = processing_time / total_time
network_time_ratio = network_time / total_time

# 识别主要瓶颈
if submit_time_ratio > 0.3:
    print("提交阶段是瓶颈")
elif processing_time_ratio > 0.6:
    print("服务器处理是瓶颈") 
else:
    print("网络延迟是瓶颈")
```

#### C. 优化建议
1. **如果提交是瓶颈**：
   - 增加客户端连接池大小
   - 优化HTTP请求参数
   - 考虑批量提交API

2. **如果处理是瓶颈**：
   - 这是服务器端瓶颈，需要服务器优化
   - 可以测试不同类型任务的处理能力

3. **如果网络是瓶颈**：
   - 优化网络环境
   - 考虑CDN或就近部署

## 📈 预期测试结果

### 典型性能表现
```
并发度    提交QPS    服务器QPS    P95响应时间    成功率
5        4.8        4.5          2.1s          100%
10       8.2        7.8          2.8s          100%
20       12.5       11.2         4.2s          98%
50       18.3       15.6         8.5s          95%
100      22.1       16.8         15.2s         85%
```

### 性能瓶颈识别
- **最优并发度**：通常在20-50之间
- **性能拐点**：超过某个并发度后QPS不再增长
- **稳定性阈值**：成功率开始显著下降的并发度

这个测试方案能够帮助您准确评估感知API服务器的并发处理能力，识别性能瓶颈，并为生产环境配置提供数据支持。
