#!/usr/bin/env python3
"""
服务器端并发性能测试脚本

专门设计用于准确测试感知API服务器的并发处理能力
"""

import os
import time
import threading
import requests
from concurrent.futures import ThreadPoolExecutor, as_completed
from collections import defaultdict, deque
import statistics
import json
from dataclasses import dataclass
from typing import List, Dict, Optional, Tuple
import queue

from dotenv import load_dotenv
from py_qj_robots import Perception


@dataclass
class ConcurrencyTestResult:
    """并发测试结果"""
    test_name: str
    concurrency_level: int
    total_requests: int
    successful_requests: int
    failed_requests: int
    test_duration: float
    
    # 提交阶段指标
    submit_duration: float
    submit_qps: float
    
    # 服务器处理指标
    server_qps: float  # 服务器实际处理QPS
    avg_server_processing_time: float  # 服务器平均处理时间
    
    # 响应时间指标
    avg_response_time: float
    p50_response_time: float
    p95_response_time: float
    p99_response_time: float
    min_response_time: float
    max_response_time: float
    
    # 错误统计
    error_types: Dict[str, int]


class ServerConcurrencyTester:
    """服务器并发性能测试器"""
    
    def __init__(self, max_workers: int = 50):
        self.max_workers = max_workers
        self.results_lock = threading.Lock()
        self.task_records = {}  # task_id -> TaskRecord
        
    def init_perception(self, max_workers: int) -> Perception:
        """初始化Perception实例"""
        load_dotenv()
        
        required_vars = ['QJ_APP_ID', 'QJ_APP_SECRET']
        missing_vars = [var for var in required_vars if not os.getenv(var)]
        if missing_vars:
            raise ValueError(f'缺少必要的环境变量: {", ".join(missing_vars)}')
        
        return Perception(enable_async_mode=True, max_workers=max_workers)
    
    def test_burst_concurrency(self, concurrency_levels: List[int], 
                              requests_per_level: int = 50) -> List[ConcurrencyTestResult]:
        """
        突发并发测试 - 测试服务器处理突发请求的能力
        
        Args:
            concurrency_levels: 并发级别列表，如 [10, 20, 50, 100]
            requests_per_level: 每个并发级别的请求数
        """
        results = []
        
        for concurrency in concurrency_levels:
            print(f"\n=== 突发并发测试: {concurrency} 并发, {requests_per_level} 请求 ===")
            
            # 重置记录
            self.task_records.clear()
            
            perception = self.init_perception(max_workers=concurrency)
            image_url = "https://cos-cdn-v1.qj-robots.com/temp/3Fvk3RNcTGeJP5GWEmRqjh8Zb23NbtL2"
            object_names = ["bottle", "cup"]
            
            # 使用线程池同时提交所有请求
            test_start_time = time.time()
            submit_times = []
            task_ids = []
            
            def submit_single_request(request_id: int) -> Optional[str]:
                """提交单个请求"""
                try:
                    submit_time = time.time()
                    task_id = perception.check_image_async(
                        image_type="2D",
                        image_url=image_url,
                        object_names=object_names,
                        callback=self._create_callback(request_id, submit_time),
                        timeout=120
                    )
                    
                    with self.results_lock:
                        submit_times.append(submit_time)
                        self.task_records[task_id] = {
                            'request_id': request_id,
                            'submit_time': submit_time,
                            'complete_time': None,
                            'success': False,
                            'error': None
                        }
                    
                    return task_id
                except Exception as e:
                    print(f"❌ 提交请求 {request_id} 失败: {e}")
                    return None
            
            # 使用线程池并发提交请求
            with ThreadPoolExecutor(max_workers=concurrency) as executor:
                futures = [executor.submit(submit_single_request, i) 
                          for i in range(requests_per_level)]
                
                for future in as_completed(futures):
                    task_id = future.result()
                    if task_id:
                        task_ids.append(task_id)
            
            submit_end_time = time.time()
            submit_duration = submit_end_time - test_start_time
            
            print(f"📤 提交完成: {len(task_ids)}/{requests_per_level} 请求, 耗时: {submit_duration:.2f}s")
            
            # 等待所有任务完成
            self._wait_for_completion(task_ids, max_wait_time=180)
            
            test_end_time = time.time()
            
            # 分析结果
            result = self._analyze_results(
                test_name=f"突发并发_{concurrency}",
                concurrency_level=concurrency,
                total_requests=requests_per_level,
                test_start_time=test_start_time,
                submit_duration=submit_duration,
                test_end_time=test_end_time
            )
            
            results.append(result)
            self._print_result(result)
            
            # 测试间隔，让服务器恢复
            time.sleep(5)
        
        return results
    
    def test_sustained_concurrency(self, concurrency: int, duration_seconds: int = 300) -> ConcurrencyTestResult:
        """
        持续并发测试 - 测试服务器长时间处理并发请求的能力
        
        Args:
            concurrency: 并发级别
            duration_seconds: 测试持续时间（秒）
        """
        print(f"\n=== 持续并发测试: {concurrency} 并发, {duration_seconds}s 持续时间 ===")
        
        self.task_records.clear()
        perception = self.init_perception(max_workers=concurrency)
        
        image_url = "https://cos-cdn-v1.qj-robots.com/temp/3Fvk3RNcTGeJP5GWEmRqjh8Zb23NbtL2"
        object_names = ["bottle", "cup"]
        
        test_start_time = time.time()
        request_counter = 0
        task_ids = []
        
        def continuous_submitter():
            """持续提交请求的线程"""
            nonlocal request_counter
            
            while time.time() - test_start_time < duration_seconds:
                try:
                    submit_time = time.time()
                    task_id = perception.check_image_async(
                        image_type="2D",
                        image_url=image_url,
                        object_names=object_names,
                        callback=self._create_callback(request_counter, submit_time),
                        timeout=120
                    )
                    
                    with self.results_lock:
                        self.task_records[task_id] = {
                            'request_id': request_counter,
                            'submit_time': submit_time,
                            'complete_time': None,
                            'success': False,
                            'error': None
                        }
                        task_ids.append(task_id)
                        request_counter += 1
                    
                    # 控制提交速率，避免过快提交
                    time.sleep(0.1)
                    
                except Exception as e:
                    print(f"❌ 持续提交失败: {e}")
                    time.sleep(1)
        
        # 启动多个提交线程以维持并发
        submit_threads = []
        for _ in range(min(concurrency, 10)):  # 最多10个提交线程
            thread = threading.Thread(target=continuous_submitter)
            thread.daemon = True
            thread.start()
            submit_threads.append(thread)
        
        # 等待测试时间结束
        time.sleep(duration_seconds)
        
        # 等待所有提交线程结束
        for thread in submit_threads:
            thread.join(timeout=5)
        
        submit_end_time = time.time()
        
        print(f"📤 提交阶段结束: {len(task_ids)} 请求已提交")
        
        # 等待所有任务完成
        self._wait_for_completion(task_ids, max_wait_time=300)
        
        test_end_time = time.time()
        
        # 分析结果
        result = self._analyze_results(
            test_name=f"持续并发_{concurrency}",
            concurrency_level=concurrency,
            total_requests=len(task_ids),
            test_start_time=test_start_time,
            submit_duration=submit_end_time - test_start_time,
            test_end_time=test_end_time
        )
        
        self._print_result(result)
        return result
    
    def _create_callback(self, request_id: int, submit_time: float):
        """创建回调函数"""
        def callback(result, error):
            complete_time = time.time()
            
            with self.results_lock:
                # 从result中获取task_id
                task_id = None
                if result and 'taskId' in result:
                    task_id = result['taskId']
                
                if task_id and task_id in self.task_records:
                    self.task_records[task_id].update({
                        'complete_time': complete_time,
                        'success': error is None,
                        'error': str(error) if error else None
                    })
                
                response_time = complete_time - submit_time
                status = "✅" if error is None else "❌"
                print(f"{status} 请求 {request_id} 完成, 响应时间: {response_time:.2f}s")
        
        return callback
    
    def _wait_for_completion(self, task_ids: List[str], max_wait_time: int = 300):
        """等待所有任务完成"""
        print(f"⏳ 等待 {len(task_ids)} 个任务完成 (最多等待 {max_wait_time}s)...")
        
        start_wait = time.time()
        last_report = start_wait
        
        while time.time() - start_wait < max_wait_time:
            with self.results_lock:
                completed = sum(1 for record in self.task_records.values() 
                              if record.get('complete_time') is not None)
            
            # 每10秒报告一次进度
            if time.time() - last_report >= 10:
                print(f"📊 进度: {completed}/{len(task_ids)} 完成")
                last_report = time.time()
            
            if completed >= len(task_ids):
                break
            
            time.sleep(2)
        
        with self.results_lock:
            final_completed = sum(1 for record in self.task_records.values() 
                                if record.get('complete_time') is not None)
        
        print(f"🏁 等待结束: {final_completed}/{len(task_ids)} 任务完成")

    def _analyze_results(self, test_name: str, concurrency_level: int, total_requests: int,
                        test_start_time: float, submit_duration: float, test_end_time: float) -> ConcurrencyTestResult:
        """分析测试结果"""

        with self.results_lock:
            # 统计基本指标
            successful_requests = sum(1 for record in self.task_records.values()
                                    if record.get('success', False))
            failed_requests = total_requests - successful_requests

            # 计算响应时间
            response_times = []
            server_processing_times = []

            for record in self.task_records.values():
                if record.get('complete_time') and record.get('submit_time'):
                    response_time = record['complete_time'] - record['submit_time']
                    response_times.append(response_time)

                    # 估算服务器处理时间（响应时间减去网络延迟，这里简化处理）
                    # 实际应该通过服务器日志或API返回的处理时间来获取
                    estimated_server_time = max(0.1, response_time - 0.2)  # 假设网络延迟200ms
                    server_processing_times.append(estimated_server_time)

            # 计算错误类型统计
            error_types = defaultdict(int)
            for record in self.task_records.values():
                if record.get('error'):
                    error_type = type(record['error']).__name__ if hasattr(record['error'], '__class__') else 'Unknown'
                    error_types[error_type] += 1

            # 计算QPS
            test_duration = test_end_time - test_start_time
            submit_qps = total_requests / submit_duration if submit_duration > 0 else 0
            server_qps = successful_requests / test_duration if test_duration > 0 else 0

            # 计算响应时间统计
            if response_times:
                response_times.sort()
                n = len(response_times)
                p50_response_time = response_times[int(n * 0.5)]
                p95_response_time = response_times[int(n * 0.95)]
                p99_response_time = response_times[int(n * 0.99)]
                avg_response_time = statistics.mean(response_times)
                min_response_time = min(response_times)
                max_response_time = max(response_times)
            else:
                p50_response_time = p95_response_time = p99_response_time = 0
                avg_response_time = min_response_time = max_response_time = 0

            avg_server_processing_time = statistics.mean(server_processing_times) if server_processing_times else 0

        return ConcurrencyTestResult(
            test_name=test_name,
            concurrency_level=concurrency_level,
            total_requests=total_requests,
            successful_requests=successful_requests,
            failed_requests=failed_requests,
            test_duration=test_duration,
            submit_duration=submit_duration,
            submit_qps=submit_qps,
            server_qps=server_qps,
            avg_server_processing_time=avg_server_processing_time,
            avg_response_time=avg_response_time,
            p50_response_time=p50_response_time,
            p95_response_time=p95_response_time,
            p99_response_time=p99_response_time,
            min_response_time=min_response_time,
            max_response_time=max_response_time,
            error_types=dict(error_types)
        )

    def _print_result(self, result: ConcurrencyTestResult):
        """打印测试结果"""
        print(f"\n{'='*60}")
        print(f"📊 {result.test_name} 测试结果")
        print(f"{'='*60}")
        print(f"并发级别: {result.concurrency_level}")
        print(f"总请求数: {result.total_requests}")
        print(f"成功请求: {result.successful_requests}")
        print(f"失败请求: {result.failed_requests}")
        print(f"成功率: {result.successful_requests/result.total_requests*100:.1f}%")
        print(f"")
        print(f"⏱️  时间指标:")
        print(f"  测试总时间: {result.test_duration:.2f}s")
        print(f"  提交时间: {result.submit_duration:.2f}s")
        print(f"")
        print(f"🚀 QPS指标:")
        print(f"  提交QPS: {result.submit_qps:.2f}")
        print(f"  服务器QPS: {result.server_qps:.2f}")
        print(f"")
        print(f"📈 响应时间指标:")
        print(f"  平均响应时间: {result.avg_response_time:.2f}s")
        print(f"  P50响应时间: {result.p50_response_time:.2f}s")
        print(f"  P95响应时间: {result.p95_response_time:.2f}s")
        print(f"  P99响应时间: {result.p99_response_time:.2f}s")
        print(f"  最小响应时间: {result.min_response_time:.2f}s")
        print(f"  最大响应时间: {result.max_response_time:.2f}s")
        print(f"  估算服务器处理时间: {result.avg_server_processing_time:.2f}s")

        if result.error_types:
            print(f"")
            print(f"❌ 错误统计:")
            for error_type, count in result.error_types.items():
                print(f"  {error_type}: {count}")

    def save_results_to_csv(self, results: List[ConcurrencyTestResult], filename: str = "concurrency_test_results.csv"):
        """保存结果到CSV文件"""
        import csv

        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = [
                'test_name', 'concurrency_level', 'total_requests', 'successful_requests',
                'failed_requests', 'success_rate', 'test_duration', 'submit_duration',
                'submit_qps', 'server_qps', 'avg_response_time', 'p50_response_time',
                'p95_response_time', 'p99_response_time', 'min_response_time',
                'max_response_time', 'avg_server_processing_time'
            ]

            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()

            for result in results:
                writer.writerow({
                    'test_name': result.test_name,
                    'concurrency_level': result.concurrency_level,
                    'total_requests': result.total_requests,
                    'successful_requests': result.successful_requests,
                    'failed_requests': result.failed_requests,
                    'success_rate': f"{result.successful_requests/result.total_requests*100:.1f}%",
                    'test_duration': f"{result.test_duration:.2f}",
                    'submit_duration': f"{result.submit_duration:.2f}",
                    'submit_qps': f"{result.submit_qps:.2f}",
                    'server_qps': f"{result.server_qps:.2f}",
                    'avg_response_time': f"{result.avg_response_time:.2f}",
                    'p50_response_time': f"{result.p50_response_time:.2f}",
                    'p95_response_time': f"{result.p95_response_time:.2f}",
                    'p99_response_time': f"{result.p99_response_time:.2f}",
                    'min_response_time': f"{result.min_response_time:.2f}",
                    'max_response_time': f"{result.max_response_time:.2f}",
                    'avg_server_processing_time': f"{result.avg_server_processing_time:.2f}"
                })

        print(f"📄 结果已保存到: {filename}")


def main():
    """主测试函数"""
    tester = ServerConcurrencyTester(max_workers=100)

    try:
        print("🚀 开始服务器并发性能测试")

        # 测试1: 突发并发测试
        print("\n" + "="*80)
        print("📈 突发并发测试 - 测试服务器处理突发请求的能力")
        print("="*80)

        burst_results = tester.test_burst_concurrency(
            concurrency_levels=[5, 10, 20, 30],
            requests_per_level=20
        )

        # 测试2: 持续并发测试（可选，时间较长）
        # print("\n" + "="*80)
        # print("⏱️  持续并发测试 - 测试服务器长时间处理并发的能力")
        # print("="*80)
        #
        # sustained_result = tester.test_sustained_concurrency(
        #     concurrency=10,
        #     duration_seconds=60  # 1分钟测试
        # )
        # burst_results.append(sustained_result)

        # 保存结果
        tester.save_results_to_csv(burst_results, "server_concurrency_results.csv")

        # 总结报告
        print(f"\n{'='*80}")
        print("📊 测试总结")
        print(f"{'='*80}")

        for result in burst_results:
            print(f"{result.test_name}: 并发{result.concurrency_level}, "
                  f"服务器QPS={result.server_qps:.2f}, "
                  f"成功率={result.successful_requests/result.total_requests*100:.1f}%, "
                  f"P95响应时间={result.p95_response_time:.2f}s")

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
