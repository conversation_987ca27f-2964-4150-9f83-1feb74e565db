#!/usr/bin/env python3
"""
性能调试测试脚本

专门用于调试和分析异步模式的性能问题
"""

import os
import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from collections import defaultdict
import statistics

from dotenv import load_dotenv
from py_qj_robots import Perception


def init_perception_async(max_workers=20):
    """Initialize Perception with async mode enabled"""
    load_dotenv()
    
    required_vars = ['QJ_APP_ID', 'QJ_APP_SECRET']
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    if missing_vars:
        raise ValueError(f'缺少必要的环境变量: {", ".join(missing_vars)}')
    
    return Perception(enable_async_mode=True, max_workers=max_workers)


class PerformanceDebugger:
    def __init__(self):
        self.submit_times = []
        self.completion_times = []
        self.task_results = {}
        self.lock = threading.Lock()
        self.test_start_time = None  # 测试开始的绝对时间

    def reset(self):
        """Reset all counters"""
        with self.lock:
            self.submit_times.clear()
            self.completion_times.clear()
            self.task_results.clear()
            self.test_start_time = None

    def start_test(self):
        """标记测试开始时间"""
        with self.lock:
            self.test_start_time = time.time()

    def record_submit(self, task_id, submit_time):
        """Record task submission"""
        with self.lock:
            if self.test_start_time is None:
                self.test_start_time = submit_time
            self.submit_times.append(submit_time)  # 存储绝对时间
            self.task_results[task_id] = {'submit_time': submit_time, 'complete_time': None}

    def record_completion(self, task_id, complete_time, success=True):
        """Record task completion"""
        with self.lock:
            self.completion_times.append(complete_time)  # 存储绝对时间
            if task_id in self.task_results:
                self.task_results[task_id]['complete_time'] = complete_time
                self.task_results[task_id]['success'] = success
    
    def get_stats(self):
        """Get performance statistics"""
        with self.lock:
            total_submitted = len(self.submit_times)
            total_completed = len(self.completion_times)

            if not self.submit_times or self.test_start_time is None:
                return {}

            # 计算实际的时间跨度
            submit_start_time = min(self.submit_times) if self.submit_times else self.test_start_time
            submit_end_time = max(self.submit_times) if self.submit_times else self.test_start_time
            submit_duration = submit_end_time - submit_start_time

            completion_end_time = max(self.completion_times) if self.completion_times else self.test_start_time
            completion_duration = completion_end_time - self.test_start_time  # 从测试开始到最后完成的总时间

            # Calculate response times
            response_times = []
            successful_tasks = 0

            for _, data in self.task_results.items():
                if data.get('complete_time') and data.get('submit_time'):
                    response_time = data['complete_time'] - data['submit_time']
                    response_times.append(response_time)
                    if data.get('success', False):
                        successful_tasks += 1

            # 修正QPS计算
            # 提交QPS：在提交时间段内的平均提交速率
            submit_qps = total_submitted / submit_duration if submit_duration > 0 else total_submitted
            # 整体QPS：从测试开始到所有任务完成的平均成功速率
            overall_qps = successful_tasks / completion_duration if completion_duration > 0 else 0

            stats = {
                'total_submitted': total_submitted,
                'total_completed': total_completed,
                'successful_tasks': successful_tasks,
                'submit_duration': submit_duration,
                'completion_duration': completion_duration,
                'submit_qps': submit_qps,
                'overall_qps': overall_qps,
                'avg_response_time': statistics.mean(response_times) if response_times else 0,
                'min_response_time': min(response_times) if response_times else 0,
                'max_response_time': max(response_times) if response_times else 0,
            }

            return stats


def test_async_performance_detailed(total_requests=50, max_workers=20):
    """详细的异步性能测试"""
    print(f"=== 详细异步性能测试: {total_requests} 请求, {max_workers} 工作线程 ===")

    debugger = PerformanceDebugger()
    debugger.start_test()  # 标记测试开始时间
    perception = init_perception_async(max_workers=max_workers)

    image_url = "https://cos-cdn-v1.qj-robots.com/temp/3Fvk3RNcTGeJP5GWEmRqjh8Zb23NbtL2"
    object_names = ["bottle", "cup"]

    # 存储task_id到callback的映射
    task_callbacks = {}

    def create_callback(request_index):
        def callback(result, error):
            complete_time = time.time()
            success = error is None
            # 从result中获取task_id，或者从预存的映射中获取
            task_id = None
            if result and 'taskId' in result:
                task_id = result['taskId']
            else:
                # 如果result中没有taskId，尝试从映射中查找
                for tid, cb in task_callbacks.items():
                    if cb == callback:
                        task_id = tid
                        break

            if task_id:
                debugger.record_completion(task_id, complete_time, success)

            if error:
                print(f"❌ Task {request_index} (ID: {task_id}) failed: {error}")
            else:
                print(f"✅ Task {request_index} (ID: {task_id}) completed")
        return callback

    print("开始提交任务...")

    # Phase 1: Submit all tasks and measure submission performance
    for i in range(total_requests):
        try:
            submit_time = time.time()
            callback = create_callback(i)
            task_id = perception.check_image_async(
                image_type="2D",
                image_url=image_url,
                object_names=object_names,
                callback=callback,
                timeout=60
            )
            task_callbacks[task_id] = callback  # 存储映射关系
            debugger.record_submit(task_id, submit_time)
            print(f"📤 Submitted task {i+1}/{total_requests}: {task_id}")

        except Exception as e:
            print(f"❌ Failed to submit task {i}: {e}")
    
    # Phase 2: Wait for all tasks to complete
    print("\n等待所有任务完成...")
    
    # Wait with timeout
    max_wait_time = 120  # 2 minutes max
    wait_start = time.time()
    
    while time.time() - wait_start < max_wait_time:
        stats = debugger.get_stats()
        if stats['total_completed'] >= total_requests:
            break
        
        print(f"进度: {stats['total_completed']}/{total_requests} 完成")
        time.sleep(2)
    
    # Final statistics
    final_stats = debugger.get_stats()
    
    print(f"\n=== 性能测试结果 ===")
    print(f"提交任务数: {final_stats['total_submitted']}")
    print(f"完成任务数: {final_stats['total_completed']}")
    print(f"成功任务数: {final_stats['successful_tasks']}")
    print(f"提交耗时: {final_stats['submit_duration']:.2f} 秒")
    print(f"总完成耗时: {final_stats['completion_duration']:.2f} 秒")
    print(f"提交QPS: {final_stats['submit_qps']:.2f}")
    print(f"整体QPS: {final_stats['overall_qps']:.2f}")
    print(f"平均响应时间: {final_stats['avg_response_time']:.2f} 秒")
    print(f"最小响应时间: {final_stats['min_response_time']:.2f} 秒")
    print(f"最大响应时间: {final_stats['max_response_time']:.2f} 秒")
    
    return final_stats


def test_different_worker_counts():
    """测试不同工作线程数的性能"""
    print("\n=== 测试不同工作线程数的性能 ===")
    
    worker_counts = [5, 10, 20, 30]
    results = {}
    
    for workers in worker_counts:
        print(f"\n--- 测试 {workers} 个工作线程 ---")
        try:
            stats = test_async_performance_detailed(total_requests=20, max_workers=workers)
            results[workers] = stats
            time.sleep(5)  # Wait between tests
        except Exception as e:
            print(f"测试 {workers} 工作线程失败: {e}")
            results[workers] = None
    
    # Compare results
    print(f"\n=== 工作线程数性能对比 ===")
    print(f"{'工作线程':<8} {'提交QPS':<10} {'整体QPS':<10} {'平均响应时间':<12}")
    print("-" * 50)
    
    for workers, stats in results.items():
        if stats:
            print(f"{workers:<8} {stats['submit_qps']:<10.2f} {stats['overall_qps']:<10.2f} {stats['avg_response_time']:<12.2f}")
        else:
            print(f"{workers:<8} {'失败':<10} {'失败':<10} {'失败':<12}")


def main():
    """主测试函数"""
    try:
        # Test 1: Detailed performance analysis
        test_async_performance_detailed(total_requests=30, max_workers=20)
        
        # Test 2: Different worker counts
        test_different_worker_counts()
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
