#!/bin/bash

set -e  # Exit on any error

echo "=== 开始构建和上传 py-qj-robots 包到 TestPyPI ==="

# 检查必要的工具是否安装
echo "检查依赖..."
python3 -c "import build" || { echo "错误: build 模块未安装，请运行: pip install build"; exit 1; }
python3 -c "import twine" || { echo "错误: twine 模块未安装，请运行: pip install twine"; exit 1; }

# 清理旧的构建文件
echo "清理旧的构建文件..."
rm -rf dist build *.egg-info

# 更新版本号
echo "更新版本号..."
bash update-version.sh

# 构建包
echo "构建包..."
python3 -m build

# 检查构建结果
if [ ! -d "dist" ] || [ -z "$(ls -A dist)" ]; then
    echo "错误: 构建失败，dist 目录为空"
    exit 1
fi

echo "构建成功，生成的文件:"
ls -la dist/

# 检查包的完整性
echo "检查包的完整性..."
python3 -m twine check dist/*

# 上传到 TestPyPI
echo "上传到 TestPyPI..."
python3 -m twine upload --repository testpypi dist/*

echo "=== 上传到 TestPyPI 完成 ==="
echo "可以通过以下命令安装测试版本:"
echo "pip install --index-url https://test.pypi.org/simple/ py-qj-robots"
