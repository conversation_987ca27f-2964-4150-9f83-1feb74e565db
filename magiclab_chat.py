import pyaudio, wave
import numpy as np
import socketio
import pickle
import queue
import simpleaudio as sa

wav_queue = queue.Queue(maxsize=1)
sio = socketio.Client()


def SEND(frames):
    sio.emit('user_audio', pickle.dumps(frames))


@sio.on('text_response')
def on_text_response(data):
    print("收到服务器回复:", data)
@sio.on('skill_response')
def on_skill_response(data):
    print("收到服务器回复:", data)

# **接收音频数据**
@sio.on('audio_response')
def on_audio_response(data):
    print(f"收到音频数据，大小: {len(data)} 字节")

    # **保存音频文件**
    file_path = "received_audio.wav"
    with open(file_path, "wb") as f:
        f.write(data)
    print(f"音频文件已保存: {file_path}")

    # **播放音频**
    try:
        
        wave_obj = sa.WaveObject.from_wave_file(file_path)
        play_obj = wave_obj.play()
        play_obj.wait_done()  # 等待播放完成
    except Exception as e:
        print("音频播放失败:", e)



sio.connect('http://120.48.126.4:5007')

threshold=50
patience=20

print('音量阈值 = ',threshold)
print('耐心值 = ',patience)

chunk, channels, fs, frames = 1024, 1, 16000, []
Format = pyaudio.paInt16
p = pyaudio.PyAudio()
stream = p.open(format=Format, channels=channels, rate=fs, frames_per_buffer=chunk, input=True)

frames=[]




#根据阈值的拾音，拾音后把frames发给server
while True:
    data = stream.read(chunk)
    # print('66666')
    mean=np.frombuffer(data, dtype=np.int16).mean()
    frames.append(data)
    if abs(mean)<=threshold:
        if len(frames)>10:
            frames.pop(0)
    else:
        cnt=0
        while True:
            print("recording")
            data = stream.read(chunk)
            mean=np.frombuffer(data, dtype=np.int16).mean()
            if abs(mean)<threshold:
                print("stop")
                if cnt<patience:
                    cnt+=1
                else:
                    #print('66666')
                    SEND(frames) #这里把frames发给server
                    frames=[]
                    break
            else:
                cnt=0
            frames.append(data)